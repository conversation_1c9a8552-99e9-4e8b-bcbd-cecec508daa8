'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 检查列是否已存在
    const tableDescription = await queryInterface.describeTable('user_wallets');

    if (!tableDescription.diamond) {
      await queryInterface.addColumn('user_wallets', 'diamond', {
        type: Sequelize.DECIMAL(65, 3),
        defaultValue: 0,
        allowNull: true,
        comment: '钻石货币'
      });
      console.log('✅ 成功添加 diamond 字段到 user_wallets 表');
    } else {
      console.log('✅ diamond 字段已存在，跳过添加');
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('user_wallets', 'diamond');
  }
};
