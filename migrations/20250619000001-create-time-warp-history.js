'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // 检查表是否已存在
    const tables = await queryInterface.showAllTables();
    if (!tables.includes('time_warp_histories')) {
      await queryInterface.createTable('time_warp_histories', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER.UNSIGNED
      },
      walletId: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false,
        references: {
          model: 'user_wallets',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      productId: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: true,
        references: {
          model: 'iap_products',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      purchaseId: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: true,
        references: {
          model: 'iap_purchases',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      hours: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false,
        comment: '时间跳跃小时数'
      },
      gemsEarned: {
        type: Sequelize.DECIMAL(15, 3),
        allowNull: false,
        defaultValue: 0,
        comment: '获得的GEM数量'
      },
      milkProduced: {
        type: Sequelize.DECIMAL(15, 3),
        allowNull: false,
        defaultValue: 0,
        comment: '生产的牛奶数量'
      },
      milkProcessed: {
        type: Sequelize.DECIMAL(15, 3),
        allowNull: false,
        defaultValue: 0,
        comment: '处理的牛奶数量'
      },
      farmProductionPerSecond: {
        type: Sequelize.DECIMAL(15, 3),
        allowNull: false,
        defaultValue: 0,
        comment: '农场每秒产量'
      },
      deliveryProcessingPerSecond: {
        type: Sequelize.DECIMAL(15, 3),
        allowNull: false,
        defaultValue: 0,
        comment: '出货线每秒处理量'
      },
      hasVip: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: '是否有VIP加成'
      },
      hasSpeedBoost: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: '是否有速度加成'
      },
      speedBoostMultiplier: {
        type: Sequelize.DECIMAL(5, 2),
        allowNull: false,
        defaultValue: 1.00,
        comment: '速度加成倍数'
      },
      usedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        comment: '使用时间'
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      }
    });

      // 添加索引
      await queryInterface.addIndex('time_warp_histories', ['walletId']);
      await queryInterface.addIndex('time_warp_histories', ['productId']);
      await queryInterface.addIndex('time_warp_histories', ['purchaseId']);
      await queryInterface.addIndex('time_warp_histories', ['usedAt']);
      await queryInterface.addIndex('time_warp_histories', ['walletId', 'usedAt']);
    }
  },

  async down(queryInterface, Sequelize) {
    // 检查表是否存在
    const tables = await queryInterface.showAllTables();
    if (tables.includes('time_warp_histories')) {
      await queryInterface.dropTable('time_warp_histories');
    }
  }
};
