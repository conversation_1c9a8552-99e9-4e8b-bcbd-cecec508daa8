'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const tables = await queryInterface.showAllTables();
    
    // 1. 创建任务配置表 (task_configs)
    if (!tables.includes('task_configs')) {
      await queryInterface.createTable('task_configs', {
        id: {
          type: Sequelize.INTEGER.UNSIGNED,
          autoIncrement: true,
          primaryKey: true,
          allowNull: false,
        },
        condition: {
          type: Sequelize.INTEGER,
          allowNull: false,
          defaultValue: 0,
          comment: '前置任务ID，0表示无前置条件',
        },
        type: {
          type: Sequelize.INTEGER,
          allowNull: false,
          comment: '任务类型：1-解锁区域 2-升级区域 3-升级流水线 4-邀请好友',
        },
        describe: {
          type: Sequelize.STRING(255),
          allowNull: false,
          comment: '任务描述',
        },
        price1: {
          type: Sequelize.INTEGER,
          allowNull: false,
          defaultValue: 0,
          comment: '参数1：区域ID或其他配置',
        },
        price2: {
          type: Sequelize.INTEGER,
          allowNull: false,
          defaultValue: 0,
          comment: '参数2：目标等级或数量',
        },
        diamond: {
          type: Sequelize.INTEGER,
          allowNull: false,
          defaultValue: 0,
          comment: '钻石奖励',
        },
        box: {
          type: Sequelize.INTEGER,
          allowNull: false,
          defaultValue: 0,
          comment: '宝箱奖励',
        },
        coin: {
          type: Sequelize.INTEGER,
          allowNull: false,
          defaultValue: 0,
          comment: '金币奖励',
        },
        item: {
          type: Sequelize.INTEGER,
          allowNull: false,
          defaultValue: 0,
          comment: '道具奖励',
        },
        isActive: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: true,
          comment: '是否激活',
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false,
        },
      });
    }

    // 2. 创建用户任务状态表 (user_task_statuses)
    if (!tables.includes('user_task_statuses')) {
      await queryInterface.createTable('user_task_statuses', {
        id: {
          type: Sequelize.INTEGER.UNSIGNED,
          autoIncrement: true,
          primaryKey: true,
          allowNull: false,
        },
        walletId: {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: false,
          comment: '用户钱包ID',
          references: {
            model: 'user_wallets',
            key: 'id',
          },
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE',
        },
        taskId: {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: false,
          comment: '任务配置ID',
          references: {
            model: 'task_configs',
            key: 'id',
          },
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE',
        },
        status: {
          type: Sequelize.ENUM('not_accepted', 'accepted', 'completed', 'claimed'),
          allowNull: false,
          defaultValue: 'not_accepted',
        },
        currentProgress: {
          type: Sequelize.INTEGER,
          allowNull: false,
          defaultValue: 0,
          comment: '当前进度',
        },
        targetProgress: {
          type: Sequelize.INTEGER,
          allowNull: false,
          defaultValue: 1,
          comment: '目标进度',
        },
        acceptedAt: {
          type: Sequelize.DATE,
          allowNull: true,
          comment: '接取时间',
        },
        completedAt: {
          type: Sequelize.DATE,
          allowNull: true,
          comment: '完成时间',
        },
        claimedAt: {
          type: Sequelize.DATE,
          allowNull: true,
          comment: '领取时间',
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false,
        },
      });
    }

    // 3. 创建任务配置版本表 (task_config_versions)
    if (!tables.includes('task_config_versions')) {
      await queryInterface.createTable('task_config_versions', {
        id: {
          type: Sequelize.INTEGER.UNSIGNED,
          autoIncrement: true,
          primaryKey: true,
          allowNull: false,
        },
        versionNumber: {
          type: Sequelize.STRING(50),
          allowNull: false,
          unique: true,
          comment: '版本号',
        },
        description: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: '版本描述',
        },
        configData: {
          type: Sequelize.TEXT('long'),
          allowNull: false,
          comment: '配置数据JSON',
        },
        createdBy: {
          type: Sequelize.STRING(100),
          allowNull: false,
          comment: '创建者',
        },
        isActive: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: false,
          comment: '是否为当前活跃版本',
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false,
        },
      });
    }

    // 4. 创建配置操作日志表 (task_config_logs)
    if (!tables.includes('task_config_logs')) {
      await queryInterface.createTable('task_config_logs', {
        id: {
          type: Sequelize.INTEGER.UNSIGNED,
          autoIncrement: true,
          primaryKey: true,
          allowNull: false,
        },
        action: {
          type: Sequelize.ENUM('upload', 'apply', 'rollback', 'validate'),
          allowNull: false,
          comment: '操作类型',
        },
        versionNumber: {
          type: Sequelize.STRING(50),
          allowNull: true,
          comment: '相关版本号',
        },
        details: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: '操作详情',
        },
        adminId: {
          type: Sequelize.STRING(100),
          allowNull: false,
          comment: '操作管理员ID',
        },
        status: {
          type: Sequelize.ENUM('success', 'failed'),
          allowNull: false,
          comment: '操作状态',
        },
        errorMessage: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: '错误信息',
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false,
        },
      });
    }

    // 5. 添加索引
    try {
      await queryInterface.addIndex('user_task_statuses', ['walletId', 'taskId'], {
        unique: true,
        name: 'user_task_status_unique_new',
      });
    } catch (error) {
      // 索引可能已存在，忽略错误
    }

    try {
      await queryInterface.addIndex('user_task_statuses', ['walletId', 'status']);
      await queryInterface.addIndex('task_configs', ['type']);
      await queryInterface.addIndex('task_configs', ['condition']);
      await queryInterface.addIndex('task_configs', ['isActive']);
      await queryInterface.addIndex('task_config_versions', ['isActive']);
      await queryInterface.addIndex('task_config_logs', ['action']);
      await queryInterface.addIndex('task_config_logs', ['adminId']);
    } catch (error) {
      // 索引可能已存在，忽略错误
    }
  },

  async down(queryInterface, Sequelize) {
    // 删除表（按依赖关系逆序）
    await queryInterface.dropTable('task_config_logs');
    await queryInterface.dropTable('task_config_versions');
    await queryInterface.dropTable('user_task_statuses');
    await queryInterface.dropTable('task_configs');
  }
};
