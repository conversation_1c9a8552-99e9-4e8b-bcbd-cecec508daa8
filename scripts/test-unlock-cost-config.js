#!/usr/bin/env node

/**
 * 测试解锁费用是否正确使用 farm_configs 表的数据
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

// 数据库配置
const dbConfig = {
  host: '127.0.0.1',
  port: 3669,
  user: 'wolf',
  password: '00321zixunadmin',
  database: 'wolf'
};

/**
 * 创建数据库连接
 */
async function createConnection() {
  try {
    const connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    return connection;
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    throw error;
  }
}

/**
 * 获取 farm_configs 表中的解锁费用配置
 */
async function getFarmConfigUnlockCosts(connection) {
  try {
    const [rows] = await connection.execute(`
      SELECT grade, cost 
      FROM farm_configs 
      WHERE isActive = true AND grade BETWEEN 1 AND 20
      ORDER BY grade
    `);
    
    console.log(`📊 从 farm_configs 表获取到 ${rows.length} 条解锁费用配置:`);
    
    const configMap = {};
    rows.forEach(row => {
      configMap[row.grade] = row.cost;
      console.log(`   Grade ${row.grade}: ${row.cost} GEM`);
    });
    
    return configMap;
  } catch (error) {
    console.error('❌ 获取配置失败:', error.message);
    throw error;
  }
}

/**
 * 获取农场区块的实际解锁费用（只查看测试用户的数据）
 */
async function getFarmPlotUnlockCosts(connection) {
  const testWalletId = 999999; // 测试用户ID

  try {
    const [rows] = await connection.execute(`
      SELECT plotNumber, unlockCost
      FROM farm_plots
      WHERE walletId = ? AND plotNumber BETWEEN 1 AND 20
      ORDER BY plotNumber
    `, [testWalletId]);

    console.log(`\n📊 从 farm_plots 表获取到测试用户 ${testWalletId} 的 ${rows.length} 个农场区块解锁费用:`);

    const plotMap = {};
    rows.forEach(row => {
      plotMap[row.plotNumber] = parseFloat(row.unlockCost);
      console.log(`   Plot ${row.plotNumber}: ${row.unlockCost} GEM`);
    });

    return plotMap;
  } catch (error) {
    console.error('❌ 获取农场区块解锁费用失败:', error.message);
    throw error;
  }
}

/**
 * 验证解锁费用是否匹配
 */
function validateUnlockCosts(configMap, plotMap) {
  console.log(`\n🔍 验证解锁费用是否匹配:`);
  
  let allMatch = true;
  let matchCount = 0;
  let mismatchCount = 0;
  
  for (let plotNumber = 1; plotNumber <= 20; plotNumber++) {
    const configCost = configMap[plotNumber];
    const plotCost = plotMap[plotNumber];
    
    if (plotNumber === 1) {
      // 第一个农场区块应该免费解锁
      if (plotCost === 0) {
        console.log(`   ✅ Plot ${plotNumber}: 免费解锁 (正确)`);
        matchCount++;
      } else {
        console.log(`   ❌ Plot ${plotNumber}: 期望免费，实际 ${plotCost} GEM`);
        allMatch = false;
        mismatchCount++;
      }
    } else {
      // 其他农场区块应该使用对应 grade 的 cost
      if (configCost !== undefined && Math.abs(plotCost - configCost) < 0.001) {
        console.log(`   ✅ Plot ${plotNumber}: ${plotCost} GEM (匹配 Grade ${plotNumber})`);
        matchCount++;
      } else {
        console.log(`   ❌ Plot ${plotNumber}: 期望 ${configCost} GEM (Grade ${plotNumber})，实际 ${plotCost} GEM`);
        allMatch = false;
        mismatchCount++;
      }
    }
  }
  
  console.log(`\n📊 验证结果:`);
  console.log(`   ✅ 匹配: ${matchCount} 个`);
  console.log(`   ❌ 不匹配: ${mismatchCount} 个`);
  
  if (allMatch) {
    console.log(`\n🎉 所有解锁费用都正确匹配 farm_configs 表的配置！`);
  } else {
    console.log(`\n⚠️ 发现不匹配的解锁费用，需要检查配置`);
  }
  
  return allMatch;
}

/**
 * 主测试函数
 */
async function runTest() {
  let connection;
  
  try {
    console.log('🚀 开始解锁费用配置验证测试');
    console.log('='.repeat(60));
    
    // 创建数据库连接
    connection = await createConnection();
    
    // 获取配置数据
    const configMap = await getFarmConfigUnlockCosts(connection);
    
    // 获取农场区块数据
    const plotMap = await getFarmPlotUnlockCosts(connection);
    
    // 验证匹配性
    const allMatch = validateUnlockCosts(configMap, plotMap);
    
    console.log('\n' + '='.repeat(60));
    if (allMatch) {
      console.log('✅ 测试通过：解锁费用正确使用 farm_configs 表配置');
    } else {
      console.log('❌ 测试失败：解锁费用配置不匹配');
    }
    
  } catch (error) {
    console.error('\n❌ 测试执行失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 数据库连接已关闭');
    }
  }
}

// 运行测试
runTest();
