// 生成测试JWT token的脚本
const jwt = require('jsonwebtoken');
require('dotenv').config();

// 从环境变量获取JWT密钥
const JWT_SECRET = process.env.JWT_SECRET_Wallet;

if (!JWT_SECRET) {
  console.error('❌ JWT_SECRET_Wallet 环境变量未设置');
  process.exit(1);
}

// 生成测试token
function generateTestToken() {
  const payload = {
    userId: 1,
    walletId: 1,
    walletAddress: '******************************************'
  };
  
  const token = jwt.sign(payload, JWT_SECRET, { expiresIn: '60d' });
  return token;
}

// 验证token
function verifyToken(token) {
  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    return { valid: true, payload: decoded };
  } catch (error) {
    return { valid: false, error: error.message };
  }
}

// 主函数
function main() {
  console.log('🔑 生成测试JWT token...');
  console.log('='.repeat(60));
  
  const token = generateTestToken();
  console.log('✅ Token生成成功！');
  console.log(`Token: ${token}`);
  
  console.log('\n🔍 验证token...');
  const verification = verifyToken(token);
  
  if (verification.valid) {
    console.log('✅ Token验证成功！');
    console.log('Payload:', verification.payload);
    
    console.log('\n📋 测试配置:');
    console.log('='.repeat(60));
    console.log('请将以下token复制到测试脚本中：');
    console.log(`const JWT_TOKEN = '${token}';`);
    console.log('='.repeat(60));
    
  } else {
    console.log('❌ Token验证失败:', verification.error);
  }
}

main();
