'use strict';

// 导入BigNumber配置
const BigNumber = require('bignumber.js');

// 配置BigNumber
BigNumber.config({
  DECIMAL_PLACES: 3,
  ROUNDING_MODE: BigNumber.ROUND_HALF_UP
});

// BigNumber计算函数
function calculateBaseProduction(level) {
  const base = new BigNumber(1);
  const multiplier = new BigNumber(1.5);
  const exponent = new BigNumber(level - 1);
  return base.multipliedBy(multiplier.pow(exponent)).toNumber();
}

function calculateUpgradedSpeed(currentSpeed) {
  const current = new BigNumber(currentSpeed);
  const divisor = new BigNumber(1.05);
  return current.dividedBy(divisor).toNumber();
}

function calculateUpgradedProduction(currentProduction) {
  const current = new BigNumber(currentProduction);
  const multiplier = new BigNumber(1.5);
  return current.multipliedBy(multiplier).toNumber();
}

function calculateUpgradeCost(currentCost) {
  const current = new BigNumber(currentCost);
  const multiplier = new BigNumber(1.5);
  return current.multipliedBy(multiplier).toNumber();
}

function formatToThreeDecimals(value) {
  return parseFloat(new BigNumber(value).toFixed(3));
}

module.exports = {
  up: async (queryInterface, Sequelize) => {
    console.log('开始使用BigNumber.js更新数值精度...');

    // 检查表是否存在
    const tables = await queryInterface.showAllTables();

    // 更新农场区数据
    console.log('更新农场区数据...');
    if (!tables.includes('farm_plots')) {
      console.log('farm_plots 表不存在，跳过更新');
    } else {
      const farmPlots = await queryInterface.sequelize.query(
      'SELECT * FROM farm_plots ORDER BY walletId, plotNumber',
      { type: Sequelize.QueryTypes.SELECT }
    );
    
    console.log(`找到 ${farmPlots.length} 个农场区记录`);
    
    for (const plot of farmPlots) {
      let updates = {};
      let needsUpdate = false;
      
      // 重新计算产量（使用BigNumber）
      const newMilkProduction = plot.isUnlocked ? 
        formatToThreeDecimals(calculateBaseProduction(plot.level)) : 0;
      
      if (Math.abs(plot.milkProduction - newMilkProduction) > 0.001) {
        updates.milkProduction = newMilkProduction;
        needsUpdate = true;
      }
      
      // 重新计算生产速度（使用BigNumber）
      const baseSpeed = 5.0;
      const newProductionSpeed = formatToThreeDecimals(
        baseSpeed / Math.pow(1.05, plot.level - 1)
      );
      
      if (Math.abs(plot.productionSpeed - newProductionSpeed) > 0.001) {
        updates.productionSpeed = newProductionSpeed;
        needsUpdate = true;
      }
      
      // 重新计算升级费用（使用BigNumber）
      const baseUpgradeCost = 200;
      const newUpgradeCost = formatToThreeDecimals(
        baseUpgradeCost * Math.pow(1.5, plot.level - 1)
      );
      
      if (Math.abs(plot.upgradeCost - newUpgradeCost) > 0.001) {
        updates.upgradeCost = newUpgradeCost;
        needsUpdate = true;
      }
      
      if (needsUpdate) {
        await queryInterface.sequelize.query(
          `UPDATE farm_plots SET 
             ${Object.keys(updates).map(key => `${key} = :${key}`).join(', ')}
           WHERE id = :id`,
          {
            replacements: { ...updates, id: plot.id },
            type: Sequelize.QueryTypes.UPDATE
          }
        );
        
        console.log(`更新农场区 ${plot.plotNumber}: ${Object.keys(updates).join(', ')}`);
      }
    }
    }

    // 更新出货线数据
    console.log('\n更新出货线数据...');
    if (!tables.includes('delivery_lines')) {
      console.log('delivery_lines 表不存在，跳过更新');
    } else {
      const deliveryLines = await queryInterface.sequelize.query(
      'SELECT * FROM delivery_lines ORDER BY walletId',
      { type: Sequelize.QueryTypes.SELECT }
    );
    
    console.log(`找到 ${deliveryLines.length} 个出货线记录`);
    
    for (const line of deliveryLines) {
      let updates = {};
      let needsUpdate = false;
      
      // 重新计算出货速度（使用BigNumber）
      const baseDeliverySpeed = 5.0;
      const newDeliverySpeed = formatToThreeDecimals(
        baseDeliverySpeed / Math.pow(1.01, line.level - 1)
      );
      
      if (Math.abs(line.deliverySpeed - newDeliverySpeed) > 0.001) {
        updates.deliverySpeed = newDeliverySpeed;
        needsUpdate = true;
      }
      
      // 重新计算方块容量（使用BigNumber）
      const baseBlockUnit = 5;
      const newBlockUnit = formatToThreeDecimals(
        baseBlockUnit * Math.pow(2.0, line.level - 1)
      );
      
      if (Math.abs(line.blockUnit - newBlockUnit) > 0.001) {
        updates.blockUnit = newBlockUnit;
        needsUpdate = true;
      }
      
      // 重新计算方块价格（使用BigNumber）
      const baseBlockPrice = 5;
      const newBlockPrice = formatToThreeDecimals(
        baseBlockPrice * Math.pow(2.0, line.level - 1)
      );
      
      if (Math.abs(line.blockPrice - newBlockPrice) > 0.001) {
        updates.blockPrice = newBlockPrice;
        needsUpdate = true;
      }
      
      // 重新计算升级费用（使用BigNumber）
      const baseUpgradeCost = 500;
      const newUpgradeCost = formatToThreeDecimals(
        baseUpgradeCost * Math.pow(2.0, line.level - 1)
      );
      
      if (Math.abs(line.upgradeCost - newUpgradeCost) > 0.001) {
        updates.upgradeCost = newUpgradeCost;
        needsUpdate = true;
      }
      
      if (needsUpdate) {
        await queryInterface.sequelize.query(
          `UPDATE delivery_lines SET 
             ${Object.keys(updates).map(key => `${key} = :${key}`).join(', ')}
           WHERE id = :id`,
          {
            replacements: { ...updates, id: line.id },
            type: Sequelize.QueryTypes.UPDATE
          }
        );
        
        console.log(`更新出货线 ${line.id}: ${Object.keys(updates).join(', ')}`);
      }
    }
    }

    console.log('\nBigNumber.js 精度更新完成');

    // 验证更新结果
    if (tables.includes('farm_plots')) {
      const verifyFarmPlots = await queryInterface.sequelize.query(
      `SELECT plotNumber, level, milkProduction, productionSpeed, upgradeCost
       FROM farm_plots 
       WHERE walletId = (SELECT MIN(walletId) FROM farm_plots)
       ORDER BY plotNumber LIMIT 5`,
      { type: Sequelize.QueryTypes.SELECT }
    );
    
      console.log('\n农场区更新后数据示例:');
      verifyFarmPlots.forEach(plot => {
        console.log(`编号${plot.plotNumber}: 等级=${plot.level}, 产量=${plot.milkProduction}, 速度=${plot.productionSpeed}, 费用=${plot.upgradeCost}`);
      });
    }

    if (tables.includes('delivery_lines')) {
      const verifyDeliveryLines = await queryInterface.sequelize.query(
      `SELECT level, deliverySpeed, blockUnit, blockPrice, upgradeCost
       FROM delivery_lines 
       ORDER BY id LIMIT 3`,
      { type: Sequelize.QueryTypes.SELECT }
    );

      console.log('\n出货线更新后数据示例:');
      verifyDeliveryLines.forEach(line => {
        console.log(`等级=${line.level}, 速度=${line.deliverySpeed}, 容量=${line.blockUnit}, 价格=${line.blockPrice}, 费用=${line.upgradeCost}`);
      });
    }
  },

  down: async (queryInterface, Sequelize) => {
    console.log('回滚BigNumber.js精度更新...');
    console.log('注意：此迁移主要是精度优化，不建议回滚');
    console.log('如需回滚，请手动恢复数据或重新运行之前的迁移');
  }
};
