module.exports = {
  async up(queryInterface, Sequelize) {
    // 检查表是否存在
    const tables = await queryInterface.showAllTables();
    if (!tables.includes('chest_countdowns')) {
      console.log('chest_countdowns 表不存在，跳过添加约束');
      return;
    }

    // 检查约束是否已存在
    try {
      const constraints = await queryInterface.getForeignKeyReferencesForTable('chest_countdowns');
      const constraintExists = constraints.some(constraint => constraint.constraintName === 'fk_chest_countdowns_walletId');

      if (!constraintExists) {
        await queryInterface.addConstraint('chest_countdowns', {
      fields: ['walletId'],
      type: 'foreign key',
      name: 'fk_chest_countdowns_walletId',
      references: {
        table: 'user_wallets',
        field: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    });
      } else {
        console.log('约束 fk_chest_countdowns_walletId 已存在，跳过添加');
      }
    } catch (error) {
      console.error('检查约束时出错:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    // 检查表是否存在
    const tables = await queryInterface.showAllTables();
    if (!tables.includes('chest_countdowns')) {
      console.log('chest_countdowns 表不存在，跳过删除约束');
      return;
    }

    // 检查约束是否存在
    try {
      const constraints = await queryInterface.getForeignKeyReferencesForTable('chest_countdowns');
      const constraintExists = constraints.some(constraint => constraint.constraintName === 'fk_chest_countdowns_walletId');

      if (constraintExists) {
        await queryInterface.removeConstraint('chest_countdowns', 'fk_chest_countdowns_walletId');
      } else {
        console.log('约束 fk_chest_countdowns_walletId 不存在，跳过删除');
      }
    } catch (error) {
      console.error('检查约束时出错:', error);
      throw error;
    }
  }
};